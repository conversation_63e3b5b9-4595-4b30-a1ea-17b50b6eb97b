2025-08-01 22:02:58 [信息] AWS自动注册工具启动
2025-08-01 22:02:58 [信息] 程序版本: 1.0.0.0
2025-08-01 22:02:58 [信息] 启动时间: 2025-08-01 22:02:58
2025-08-01 22:02:58 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-01 22:02:58 [信息] 线程数量已选择: 1
2025-08-01 22:02:58 [信息] 线程数量选择初始化完成
2025-08-01 22:02:58 [信息] 程序初始化完成
2025-08-01 22:07:58 [信息] 线程数量已选择: 3
2025-08-01 22:08:17 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-01 22:08:19 [信息] 已选择文件: C:\Users\<USER>\Desktop\新建 文本文档.txt
2025-08-01 22:08:20 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\新建 文本文档.txt
2025-08-01 22:08:20 [信息] 成功加载 3 条数据
2025-08-01 22:08:22 [按钮操作] 开始注册 -> 启动注册流程
2025-08-01 22:08:22 [信息] 开始启动多线程注册，线程数量: 3
2025-08-01 22:08:22 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 3
2025-08-01 22:08:22 [信息] 所有线程已停止并清理
2025-08-01 22:08:22 [信息] 正在初始化多线程服务...
2025-08-01 22:08:22 [信息] 千川手机API服务已初始化
2025-08-01 22:08:22 [信息] 手机号码管理器已初始化，服务商: Qianchuan，将在第一个线程完成第二页后获取手机号码
2025-08-01 22:08:22 [信息] 多线程服务初始化完成
2025-08-01 22:08:22 [信息] 数据分配完成：共3条数据分配给3个线程
2025-08-01 22:08:22 [信息] 线程1分配到1条数据
2025-08-01 22:08:22 [信息] 线程2分配到1条数据
2025-08-01 22:08:22 [信息] 线程3分配到1条数据
2025-08-01 22:08:22 [信息] 屏幕工作区域: 1280x672
2025-08-01 22:08:22 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 22:08:22 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-01 22:08:22 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 22:08:22 [信息] 线程1已创建，窗口位置: (0, 0)
2025-08-01 22:08:22 [信息] 屏幕工作区域: 1280x672
2025-08-01 22:08:22 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 22:08:22 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-01 22:08:22 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 22:08:22 [信息] 线程2已创建，窗口位置: (0, 219)
2025-08-01 22:08:22 [信息] 屏幕工作区域: 1280x672
2025-08-01 22:08:22 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 22:08:22 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-01 22:08:22 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-01 22:08:22 [信息] 线程3已创建，窗口位置: (0, 438)
2025-08-01 22:08:22 [信息] 多线程注册启动成功，共3个线程
2025-08-01 22:08:22 线程1：[信息] 开始启动注册流程
2025-08-01 22:08:22 线程2：[信息] 开始启动注册流程
2025-08-01 22:08:22 线程3：[信息] 开始启动注册流程
2025-08-01 22:08:22 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-01 22:08:22 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 22:08:22 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-01 22:08:22 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-01 22:08:22 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-01 22:08:22 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-01 22:08:22 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 22:08:22 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 22:08:22 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 22:08:22 [信息] 多线程管理窗口已初始化
2025-08-01 22:08:22 [信息] UniformGrid列数已更新为: 1
2025-08-01 22:08:22 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-01 22:08:22 [信息] 多线程管理窗口已打开
2025-08-01 22:08:22 [信息] 多线程注册启动成功，共3个线程
2025-08-01 22:08:25 [信息] UniformGrid列数已更新为: 1
2025-08-01 22:08:25 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-01 22:08:25 线程1：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 22:08:25 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 22:08:25 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-08-01 22:08:25 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 22:08:26 [信息] UniformGrid列数已更新为: 1
2025-08-01 22:08:26 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-01 22:08:26 线程3：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 22:08:26 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 22:08:26 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-08-01 22:08:26 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 22:08:26 [信息] UniformGrid列数已更新为: 2
2025-08-01 22:08:26 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-01 22:08:26 线程2：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 22:08:26 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 22:08:26 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 22:08:26 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 22:08:28 线程1：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 22:08:28 线程2：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 22:08:29 线程3：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 22:08:32 线程1：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 22:08:33 线程2：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 22:08:33 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-08-01 22:08:33 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 22:08:33 线程1：[信息] 浏览器启动成功
2025-08-01 22:08:33 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-01 22:08:34 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-01 22:08:34 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 22:08:34 线程3：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 22:08:34 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 22:08:34 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 22:08:34 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-01 22:08:34 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 22:08:34 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-08-01 22:08:34 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 22:08:34 线程2：[信息] 浏览器启动成功
2025-08-01 22:08:34 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-01 22:08:34 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-01 22:08:34 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 0%)
2025-08-01 22:08:34 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 22:08:34 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 22:08:34 线程3：[信息] 浏览器启动成功
2025-08-01 22:08:34 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-01 22:08:34 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 22:08:34 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 22:08:34 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-01 22:08:35 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-01 22:08:35 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 22:08:35 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 22:08:35 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 22:08:35 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-01 22:08:35 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 22:08:35 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-01 22:08:35 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 22:08:35 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-01 22:08:35 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 22:08:35 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 22:08:35 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 22:08:35 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 22:08:35 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-01 22:08:35 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-01 22:08:35 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 22:08:36 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-01 22:08:36 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 22:08:36 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 22:08:36 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 22:08:36 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-01 22:08:36 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 22:08:36 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 22:08:36 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 22:08:36 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 22:08:50 线程3：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程3 - AWS注册 (进度: 98%)
2025-08-01 22:08:50 线程3：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 22:08:50 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 22:08:50 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 22:08:50 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 22:08:51 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 22:09:03 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 22:09:03 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-01 22:09:03 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 22:09:03 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 22:09:03 线程3：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 22:09:03 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 22:09:03 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 22:09:03 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 22:09:03 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 22:09:03 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 22:09:03 线程3：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 22:09:03 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 22:09:03 线程3：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 22:09:03 线程3：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 22:09:03 线程3：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 22:09:03 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 22:09:05 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-01 22:09:05 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 22:09:05 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 22:09:05 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 22:09:05 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 22:09:05 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 22:09:06 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 22:09:07 线程3：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35931 字节 (进度: 100%)
2025-08-01 22:09:07 线程3：[信息] [信息] ✅ 图片验证通过：201x71px，35931字节，复杂度符合要求 (进度: 100%)
2025-08-01 22:09:07 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 22:09:07 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 22:09:07 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 22:09:07 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 22:09:07 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 22:09:07 线程2：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 22:09:07 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 22:09:07 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 22:09:07 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 22:09:07 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 22:09:08 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 22:09:08 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 22:09:08 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 22:09:08 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 22:09:08 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 22:09:08 线程1：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 22:09:08 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 22:09:08 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 22:09:08 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 22:09:08 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 22:09:10 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"tftd5x"},"taskId":"186ff13a-6ee1-11f0-a5df-420da957ad82"} (进度: 100%)
2025-08-01 22:09:10 线程3：[信息] [信息] 第一页第1次识别结果: tftd5x → 转换为小写: tftd5x (进度: 100%)
2025-08-01 22:09:10 线程3：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 22:09:11 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35492 字节 (进度: 100%)
2025-08-01 22:09:11 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35492字节，复杂度符合要求 (进度: 100%)
2025-08-01 22:09:11 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 22:09:13 线程3：[信息] [信息] 已填入验证码: tftd5x (进度: 100%)
2025-08-01 22:09:13 线程3：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 22:09:13 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35312 字节 (进度: 100%)
2025-08-01 22:09:13 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35312字节，复杂度符合要求 (进度: 100%)
2025-08-01 22:09:13 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 22:09:14 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"b3m42y"},"taskId":"1ade9958-6ee1-11f0-9c52-aef288a9174b"} (进度: 100%)
2025-08-01 22:09:14 线程1：[信息] [信息] 第一页第1次识别结果: b3m42y → 转换为小写: b3m42y (进度: 100%)
2025-08-01 22:09:14 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 22:09:14 线程1：[信息] [信息] 已填入验证码: b3m42y (进度: 100%)
2025-08-01 22:09:14 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"f8yh32"},"taskId":"1ae45ffa-6ee1-11f0-9c52-aef288a9174b"} (进度: 100%)
2025-08-01 22:09:14 线程2：[信息] [信息] 第一页第1次识别结果: f8yh32 → 转换为小写: f8yh32 (进度: 100%)
2025-08-01 22:09:14 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 22:09:14 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 22:09:14 线程2：[信息] [信息] 已填入验证码: f8yh32 (进度: 100%)
2025-08-01 22:09:15 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 22:09:15 线程3：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-01 22:09:15 线程3：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-01 22:09:15 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 22:09:15 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 22:09:15 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 22:09:15 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 22:09:15 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 22:09:15 线程3：[信息] 账户注册流程已启动: <EMAIL>
2025-08-01 22:09:15 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 22:09:15 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 22:09:15 [信息] [线程3] 已删除旧的响应文件
2025-08-01 22:09:15 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-01 22:09:16 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-01 22:09:16 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-01 22:09:16 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 22:09:16 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 22:09:16 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 22:09:16 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 22:09:16 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 22:09:16 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 22:09:16 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-01 22:09:16 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-01 22:09:16 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 22:09:17 线程2：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-01 22:09:17 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-01 22:09:17 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 22:09:17 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 22:09:17 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 22:09:17 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 22:09:17 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 22:09:17 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 22:09:17 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-01 22:09:17 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 22:09:17 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-01 22:09:17 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 22:09:17 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:09:17
2025-08-01 22:09:18 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 22:09:18 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 22:09:18
2025-08-01 22:09:19 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 22:09:19 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 22:09:19
2025-08-01 22:09:20 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 22:09:20 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:09:20
2025-08-01 22:09:22 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 22:09:22 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 22:09:22
2025-08-01 22:09:22 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 22:09:22 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 22:09:22
2025-08-01 22:09:23 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-01 22:09:23 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:09:23
2025-08-01 22:09:23 [信息] [线程3] 邮箱验证码获取成功: 513675，立即停止重复请求
2025-08-01 22:09:23 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-01 22:09:23 [信息] [线程3] 已清理响应文件
2025-08-01 22:09:23 线程3：[信息] [信息] 验证码获取成功: 513675，正在自动填入... (进度: 25%)
2025-08-01 22:09:23 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-01 22:09:23 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-01 22:09:24 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-01 22:09:24 [信息] 线程3完成第二页事件已处理
2025-08-01 22:09:24 [信息] 线程3完成第二页，开始批量获取手机号码...
2025-08-01 22:09:24 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 35%)
2025-08-01 22:09:24 [信息] 开始批量获取3个手机号码，服务商: Qianchuan
2025-08-01 22:09:24 [信息] 千川API开始逐个获取3个手机号码
2025-08-01 22:09:24 [信息] [千川API] 获取手机号码，尝试 1/4
2025-08-01 22:09:24 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-**************-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-01 22:09:24 [信息] [线程2] 邮箱验证码获取成功: 730959，立即停止重复请求
2025-08-01 22:09:24 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-01 22:09:24 [信息] [线程2] 已清理响应文件
2025-08-01 22:09:24 线程2：[信息] [信息] 验证码获取成功: 730959，正在自动填入... (进度: 25%)
2025-08-01 22:09:24 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-01 22:09:24 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-01 22:09:25 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-01 22:09:25 [信息] 线程2完成第二页事件已处理
2025-08-01 22:09:25 [信息] 线程2完成第二页，手机号码已获取，无需重复获取
2025-08-01 22:09:25 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-01 22:09:25 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-01 22:09:25 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 22:09:25
2025-08-01 22:09:25 [信息] [线程1] 邮箱验证码获取成功: 907623，立即停止重复请求
2025-08-01 22:09:25 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-01 22:09:25 [信息] [线程1] 已清理响应文件
2025-08-01 22:09:25 线程1：[信息] [信息] 验证码获取成功: 907623，正在自动填入... (进度: 25%)
2025-08-01 22:09:25 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-01 22:09:25 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-01 22:09:25 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-01 22:09:25 [信息] 线程1完成第二页事件已处理
2025-08-01 22:09:25 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-01 22:09:25 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-01 22:09:27 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-01 22:09:27 线程3：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-01 22:09:27 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870302566","phoneId":"5096602566457","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5791755,"phoneNo":"4367870302566","projectId":804413,"startTime":"2025-08-01 22:09:27","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"aea4e467-76bb-4aec-95fc-11da84d0ff0c"}
2025-08-01 22:09:27 [信息] [千川API] 获取手机号码成功: +4367870302566
2025-08-01 22:09:27 [信息] 线程1分配千川手机号码: +4367870302566
2025-08-01 22:09:28 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-01 22:09:28 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-01 22:09:28 [信息] [千川API] 获取手机号码，尝试 1/4
2025-08-01 22:09:28 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-**************-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-01 22:09:29 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-01 22:09:29 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-01 22:09:29 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 22:09:29 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 22:09:29 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-01 22:09:31 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870315692","phoneId":"5097015692443","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5791756,"phoneNo":"4367870315692","projectId":804413,"startTime":"2025-08-01 22:09:30","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"331aacc2-8e02-4b7b-a6cd-3aa77b7ef874"}
2025-08-01 22:09:31 [信息] [千川API] 获取手机号码成功: +4367870315692
2025-08-01 22:09:31 [信息] 线程2分配千川手机号码: +4367870315692
2025-08-01 22:09:31 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-01 22:09:32 [信息] [千川API] 获取手机号码，尝试 1/4
2025-08-01 22:09:32 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-**************-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-01 22:09:34 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-01 22:09:34 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-01 22:09:34 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-01 22:09:36 线程3：[信息] [信息] 第三页执行失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("input[name*='password']") to be visible (进度: 38%)
2025-08-01 22:09:36 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870318665","phoneId":"5097618665500","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5791758,"phoneNo":"4367870318665","projectId":804413,"startTime":"2025-08-01 22:09:36","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"5cec29fe-302b-49c2-a536-6ad8dd3ca082"}
2025-08-01 22:09:36 [信息] [千川API] 获取手机号码成功: +4367870318665
2025-08-01 22:09:36 [信息] 线程3分配千川手机号码: +4367870318665
2025-08-01 22:09:36 [信息] 千川API逐个获取手机号码成功，共3个
2025-08-01 22:09:36 [信息] 批量获取3个手机号码成功
2025-08-01 22:09:38 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-01 22:09:38 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-01 22:09:38 线程1：[信息] [信息] 第三页执行失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("input[name*='password']") to be visible (进度: 38%)
2025-08-01 22:09:55 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-01 22:09:56 [信息] 线程2获取已分配的千川手机号码: +4367870315692
2025-08-01 22:09:56 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +4367870315692 (进度: 38%)
2025-08-01 22:09:57 线程2：[信息] [信息] 数据国家代码为AZ，需要选择Azerbaijan (进度: 38%)
2025-08-01 22:09:58 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-01 22:10:02 线程2：[信息] [信息] 已选择国家: Azerbaijan (进度: 38%)
2025-08-01 22:10:02 线程2：[信息] [信息] 已成功选择国家: Azerbaijan (进度: 38%)
2025-08-01 22:10:02 线程2：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 38%)
2025-08-01 22:10:02 线程2：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 38%)
2025-08-01 22:10:02 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-01 22:10:02 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 38%)
2025-08-01 22:10:02 线程1：[信息] [信息]  进行智能页面检测... (进度: 38%)
2025-08-01 22:10:02 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 38%)
2025-08-01 22:10:02 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 38%)
2025-08-01 22:10:02 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 38%)
2025-08-01 22:10:02 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-01 22:10:02 线程1：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-01 22:10:02 线程1：[信息] [信息] 智能检测到当前在第1页，开始智能处理... (进度: 100%)
2025-08-01 22:10:02 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-01 22:10:02 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-01 22:10:02 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 22:10:03 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-01 22:10:04 线程2：[信息] [信息] 已选择国家代码 +43 (进度: 38%)
2025-08-01 22:10:04 线程3：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 38%)
2025-08-01 22:10:04 线程3：[信息] [信息]  进行智能页面检测... (进度: 38%)
2025-08-01 22:10:04 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 38%)
2025-08-01 22:10:04 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 38%)
2025-08-01 22:10:04 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify' → 第2页 (进度: 38%)
2025-08-01 22:10:04 线程3：[信息] [信息] 🔍 疑似第2页，进行二次确认... (进度: 38%)
2025-08-01 22:10:04 线程3：[信息] [信息] ✅ 确认为第2页：找到Verification code输入框 (进度: 100%)
2025-08-01 22:10:04 线程3：[信息] [信息]  智能检测到当前在第2页 (进度: 100%)
2025-08-01 22:10:04 线程3：[信息] [信息] 智能检测到当前在第2页，开始智能处理... (进度: 100%)
2025-08-01 22:10:04 线程3：[信息] [信息] 智能检测到第2页，检查邮箱验证码模式... (进度: 100%)
2025-08-01 22:10:04 线程3：[信息] [信息] 邮箱验证码自动模式，继续自动执行... (进度: 100%)
2025-08-01 22:10:04 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 22:10:04 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 22:10:04 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 22:10:04 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 22:10:04 [信息] [线程3] 已删除旧的响应文件
2025-08-01 22:10:04 线程3：[信息] 已继续
2025-08-01 22:10:04 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-01 22:10:04 [信息] 线程3已继续
2025-08-01 22:10:05 线程2：[信息] [信息] 开始检查手机号码状态... (进度: 38%)
2025-08-01 22:10:05 线程2：[信息] [信息]  检测到已获取API手机号码: +4367870315692，尝试自动填写... (进度: 38%)
2025-08-01 22:10:05 线程2：[信息] [信息]  已自动填写手机号码: 67870315692，直接继续下一步... (进度: 38%)
2025-08-01 22:10:05 线程2：[信息] [信息] 使用已获取的手机号码: +4367870315692（保存本地号码: 67870315692） (进度: 38%)
2025-08-01 22:10:05 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-01 22:10:06 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-01 22:10:06 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 22:10:06 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 22:10:06 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 22:10:06 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 22:10:06 线程1：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 22:10:06 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 22:10:06 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 22:10:06 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 22:10:06 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 22:10:06 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 22:10:06 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:10:06
2025-08-01 22:10:08 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-01 22:10:09 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35402 字节 (进度: 100%)
2025-08-01 22:10:09 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35402字节，复杂度符合要求 (进度: 100%)
2025-08-01 22:10:09 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 22:10:09 线程2：[信息] [信息] 正在选择月份: April (进度: 38%)
2025-08-01 22:10:09 线程2：[信息] [信息] 已选择月份（标准选项）: April (进度: 38%)
2025-08-01 22:10:09 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 22:10:09 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:10:09
2025-08-01 22:10:10 线程2：[信息] [信息] 正在选择年份: 2026 (进度: 38%)
2025-08-01 22:10:11 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"5456y6"},"taskId":"3cba8956-6ee1-11f0-a5df-420da957ad82"} (进度: 100%)
2025-08-01 22:10:11 线程1：[信息] [信息] 第一页第1次识别结果: 5456y6 → 转换为小写: 5456y6 (进度: 100%)
2025-08-01 22:10:11 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 22:10:11 线程1：[信息] [信息] 已填入验证码: 5456y6 (进度: 100%)
2025-08-01 22:10:11 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 22:10:12 线程2：[信息] [信息] 标准选项方法失败，尝试其他方法... (进度: 38%)
2025-08-01 22:10:12 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-01 22:10:12 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:10:12
2025-08-01 22:10:13 [信息] [线程3] 邮箱验证码获取成功: 513675，立即停止重复请求
2025-08-01 22:10:13 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-01 22:10:13 [信息] [线程3] 已清理响应文件
2025-08-01 22:10:13 线程3：[信息] [信息] 验证码获取成功: 513675，正在自动填入... (进度: 100%)
2025-08-01 22:10:13 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-01 22:10:13 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-01 22:10:13 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-01 22:10:13 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 22:10:13 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 22:10:14 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 22:10:14 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 22:10:14 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 22:10:14 线程1：[信息] 已继续
2025-08-01 22:10:14 [信息] 线程1已继续
2025-08-01 22:10:14 线程2：[信息] [信息] 已选择年份（下拉菜单上下文）: 2026 (进度: 38%)
2025-08-01 22:10:14 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 22:10:14 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-01 22:10:14 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-01 22:10:14 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-01 22:10:14 [信息] 线程3完成第二页事件已处理
2025-08-01 22:10:14 [信息] 线程3完成第二页，手机号码已获取，无需重复获取
2025-08-01 22:10:14 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 100%)
2025-08-01 22:10:15 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-01 22:10:15 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-01 22:10:15 线程2：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 38%)
2025-08-01 22:10:15 线程2：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 38%)
2025-08-01 22:10:16 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 22:10:17 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 22:10:16
2025-08-01 22:10:17 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-01 22:10:17 线程3：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-01 22:10:19 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 22:10:19 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 22:10:19
2025-08-01 22:10:19 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-01 22:10:22 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-01 22:10:22 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 22:10:22
2025-08-01 22:10:22 线程2：[信息] [信息] 已选择国家代码: +43 (进度: 38%)
2025-08-01 22:10:22 线程2：[信息] [信息] 已清空并重新填写手机号码: 67870315692 (进度: 38%)
2025-08-01 22:10:22 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-01 22:10:22 [信息] [线程1] 邮箱验证码获取成功: 907623，立即停止重复请求
2025-08-01 22:10:22 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-01 22:10:22 [信息] [线程1] 已清理响应文件
2025-08-01 22:10:22 线程1：[信息] [信息] 验证码获取成功: 907623，正在自动填入... (进度: 100%)
2025-08-01 22:10:22 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-01 22:10:22 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-01 22:10:22 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-01 22:10:22 [信息] 线程1完成第二页事件已处理
2025-08-01 22:10:22 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-01 22:10:22 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 100%)
2025-08-01 22:10:24 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-01 22:10:24 线程2：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 38%)
2025-08-01 22:10:24 [信息] 检测到错误信息，开始重试机制
2025-08-01 22:10:24 线程2：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 38%)
2025-08-01 22:10:24 [信息] 第1次重试发送验证码按钮
2025-08-01 22:10:26 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-01 22:10:26 线程1：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-01 22:10:26 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-01 22:10:26 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-01 22:10:26 线程1：[信息] 已暂停
2025-08-01 22:10:26 [信息] 线程1已暂停
2025-08-01 22:10:26 [信息] 线程1已暂停
2025-08-01 22:10:27 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-01 22:10:27 线程3：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-01 22:10:27 线程3：[信息] 已暂停
2025-08-01 22:10:27 [信息] 线程3已暂停
2025-08-01 22:10:27 [信息] 线程3已暂停
2025-08-01 22:10:27 线程2：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-01 22:10:27 [信息] 第1次重试：已点击发送验证码按钮
2025-08-01 22:10:28 线程3：[信息] [信息] 第三页执行失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("input[name*='password']") to be visible (进度: 100%)
2025-08-01 22:10:29 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-01 22:10:29 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-01 22:10:29 线程2：[信息] [信息] ❌ 第1次重试失败：错误信息仍然存在 (进度: 100%)
2025-08-01 22:10:29 [信息] 第1次重试失败：错误信息仍然存在
2025-08-01 22:10:29 线程2：[信息] [信息] 🔄 第2次重试发送验证码按钮... (进度: 100%)
2025-08-01 22:10:29 [信息] 第2次重试发送验证码按钮
2025-08-01 22:10:32 线程2：[信息] [信息] ✅ 第2次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-01 22:10:32 [信息] 第2次重试：已点击发送验证码按钮
2025-08-01 22:10:36 线程1：[信息] [信息] 第三页执行失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("input[name*='password']") to be visible (进度: 100%)
2025-08-01 22:10:37 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-01 22:10:37 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-01 22:10:37 线程2：[信息] [信息] ❌ 第2次重试失败：错误信息仍然存在 (进度: 100%)
2025-08-01 22:10:37 [信息] 第2次重试失败：错误信息仍然存在
2025-08-01 22:10:37 线程2：[信息] [信息] 🔄 第3次重试发送验证码按钮... (进度: 100%)
2025-08-01 22:10:37 [信息] 第3次重试发送验证码按钮
2025-08-01 22:10:40 线程2：[信息] [信息] ✅ 第3次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-01 22:10:40 [信息] 第3次重试：已点击发送验证码按钮
2025-08-01 22:10:44 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-01 22:10:44 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-01 22:10:44 线程2：[信息] [信息] ❌ 第3次重试失败：错误信息仍然存在 (进度: 100%)
2025-08-01 22:10:44 [信息] 第3次重试失败：错误信息仍然存在
2025-08-01 22:10:44 线程2：[信息] [信息] 🔄 第4次重试发送验证码按钮... (进度: 100%)
2025-08-01 22:10:44 [信息] 第4次重试发送验证码按钮
2025-08-01 22:10:47 线程2：[信息] [信息] ✅ 第4次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-01 22:10:47 [信息] 第4次重试：已点击发送验证码按钮
2025-08-01 22:10:49 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-01 22:10:49 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-01 22:10:52 线程2：[信息] [信息] ❌ 第4次重试失败：错误信息仍然存在 (进度: 100%)
2025-08-01 22:10:52 [信息] 第4次重试失败：错误信息仍然存在
2025-08-01 22:10:52 线程2：[信息] [信息] ❌ 4次重试均失败，确认为验证手机区号错误 (进度: 100%)
2025-08-01 22:10:52 [信息] 4次重试均失败，确认为验证手机区号错误
2025-08-01 22:10:52 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-01 22:10:52 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：5V15n85hH ③AWS密码：33xJot4N7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号 (进度: 100%)
2025-08-01 22:10:52 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：5V15n85hH ③AWS密码：33xJot4N7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号
2025-08-01 22:10:52 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：5V15n85hH ③AWS密码：33xJot4N7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号
2025-08-01 22:10:52 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：5V15n85hH ③AWS密码：33xJot4N7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号
2025-08-01 22:10:52 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：5V15n85hH ③AWS密码：33xJot4N7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号
2025-08-01 22:10:52 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：5V15n85hH ③AWS密码：33xJot4N7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号
2025-08-01 22:10:52 线程2：[信息] 收到失败数据保存请求: 验证手机出现区号, 数据: <EMAIL>
2025-08-01 22:10:52 [信息] 线程2请求保存失败数据: 验证手机出现区号, 数据: <EMAIL>
2025-08-01 22:10:52 [信息] 已处理失败数据: <EMAIL>, 失败原因: 验证手机出现区号
2025-08-01 22:10:52 [信息] 线程2失败数据已保存: 验证手机出现区号, 数据: <EMAIL>
2025-08-01 22:10:52 线程2：[信息] [信息] 已通知保存失败数据，失败原因: 验证手机出现区号 (进度: 0%)
2025-08-01 22:10:52 线程2：[信息] [信息] ❌ 注册失败，验证手机出现区号 (进度: 0%)
2025-08-01 22:10:52 [信息] 多线程模式：注册失败，验证手机出现区号
2025-08-01 22:10:52 线程2：[信息] [信息] 检测到验证手机区号错误，注册已终止 (进度: 0%)
2025-08-01 22:11:12 线程3：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 100%)
2025-08-01 22:11:12 线程3：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-01 22:11:12 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-01 22:11:12 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-01 22:11:13 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-01 22:11:13 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-01 22:11:13 线程3：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-01 22:11:13 线程3：[信息] [信息] 智能检测到当前在第1页，开始智能处理... (进度: 100%)
2025-08-01 22:11:13 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-01 22:11:13 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-01 22:11:13 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 22:11:13 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-01 22:11:16 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-01 22:11:16 线程3：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 22:11:16 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 22:11:16 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 22:11:16 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 22:11:16 线程3：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 22:11:16 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 22:11:16 线程3：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 22:11:16 线程3：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 22:11:16 线程3：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 22:11:20 线程3：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35338 字节 (进度: 100%)
2025-08-01 22:11:20 线程3：[信息] [信息] ✅ 图片验证通过：201x71px，35338字节，复杂度符合要求 (进度: 100%)
2025-08-01 22:11:20 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 22:11:22 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"rg2ygs"},"taskId":"6722e148-6ee1-11f0-9d4c-420da957ad82"} (进度: 100%)
2025-08-01 22:11:22 线程3：[信息] [信息] 第一页第1次识别结果: rg2ygs → 转换为小写: rg2ygs (进度: 100%)
2025-08-01 22:11:22 线程3：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 22:11:22 线程3：[信息] [信息] 已填入验证码: rg2ygs (进度: 100%)
2025-08-01 22:11:22 线程3：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 22:11:24 线程3：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-01 22:11:24 线程3：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-01 22:11:24 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 22:11:24 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 22:11:27 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-01 22:11:27 线程3：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-01 22:11:27 线程3：[信息] 已暂停
2025-08-01 22:11:27 [信息] 线程3已暂停
2025-08-01 22:11:27 [信息] 线程3已暂停
2025-08-01 22:11:27 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 22:11:27 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 22:11:27 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 22:11:27 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 22:11:27 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-01 22:11:27 线程3：[信息] 已继续
2025-08-01 22:11:27 [信息] 线程3已继续
2025-08-01 22:11:29 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 22:11:29 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:11:29
2025-08-01 22:11:33 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 22:11:33 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:11:33
2025-08-01 22:11:36 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-01 22:11:36 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:11:36
2025-08-01 22:11:39 [信息] [线程3] 第4次触发邮箱验证码获取...（最多20次）
2025-08-01 22:11:39 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:11:39
2025-08-01 22:11:42 [信息] [线程3] 第5次触发邮箱验证码获取...（最多20次）
2025-08-01 22:11:42 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:11:42
2025-08-01 22:11:45 [信息] [线程3] 第6次触发邮箱验证码获取...（最多20次）
2025-08-01 22:11:45 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:11:45
2025-08-01 22:11:48 [信息] [线程3] 第7次触发邮箱验证码获取...（最多20次）
2025-08-01 22:11:48 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:11:48
2025-08-01 22:11:51 [信息] [线程3] 第8次触发邮箱验证码获取...（最多20次）
2025-08-01 22:11:51 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:11:51
2025-08-01 22:11:55 [信息] [线程3] 第9次触发邮箱验证码获取...（最多20次）
2025-08-01 22:11:55 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:11:55
2025-08-01 22:11:58 [信息] [线程3] 第10次触发邮箱验证码获取...（最多20次）
2025-08-01 22:11:58 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:11:58
2025-08-01 22:12:01 [信息] [线程3] 第11次触发邮箱验证码获取...（最多20次）
2025-08-01 22:12:01 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:12:01
2025-08-01 22:12:05 [信息] [线程3] 第12次触发邮箱验证码获取...（最多20次）
2025-08-01 22:12:05 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:12:05
2025-08-01 22:12:08 [信息] [线程3] 第13次触发邮箱验证码获取...（最多20次）
2025-08-01 22:12:08 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:12:08
2025-08-01 22:12:11 [信息] [线程3] 第14次触发邮箱验证码获取...（最多20次）
2025-08-01 22:12:11 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:12:11
2025-08-01 22:12:14 [信息] [线程3] 第15次触发邮箱验证码获取...（最多20次）
2025-08-01 22:12:14 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:12:14
2025-08-01 22:12:17 [信息] [线程3] 第16次触发邮箱验证码获取...（最多20次）
2025-08-01 22:12:18 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:12:18
2025-08-01 22:12:21 [信息] [线程3] 第17次触发邮箱验证码获取...（最多20次）
2025-08-01 22:12:21 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:12:21
2025-08-01 22:12:24 [信息] [线程3] 第18次触发邮箱验证码获取...（最多20次）
2025-08-01 22:12:24 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:12:24
2025-08-01 22:12:27 [信息] [线程3] 第19次触发邮箱验证码获取...（最多20次）
2025-08-01 22:12:27 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:12:27
2025-08-01 22:12:30 [信息] [线程3] 第20次触发邮箱验证码获取...（最多20次）
2025-08-01 22:12:30 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 22:12:30
2025-08-01 22:12:30 [警告] [线程3] 邮箱验证码获取失败（达到最大重试次数），共尝试20次
2025-08-01 22:12:30 [信息] [线程3] 已清理请求文件
2025-08-01 22:12:30 [信息] [线程3] 已清理响应文件
2025-08-01 22:12:30 线程3：[信息] [信息] 邮箱验证码自动获取失败: 邮箱验证码获取失败（达到最大重试次数），共尝试20次 (进度: 100%)
2025-08-01 22:12:31 线程3：[信息] [信息] 🔴 Microsoft获取验证码失败，转为手动模式 (进度: 100%)
2025-08-01 22:13:39 [信息] 多线程窗口引用已清理
2025-08-01 22:13:39 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-01 22:13:39 [信息] 多线程管理窗口正在关闭
2025-08-01 22:13:41 [信息] 程序正在退出，开始清理工作...
2025-08-01 22:13:41 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-01 22:13:41 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-01 22:13:41 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-01 22:13:41 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-01 22:13:41 [信息] 程序退出清理工作完成
